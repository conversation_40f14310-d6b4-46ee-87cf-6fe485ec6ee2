import { FieldDetector } from '../utils/fieldDetection';
import { OverlayManager } from '../utils/overlayManager';
import { AutofillField, AutofillButtonPosition, FieldDetectionResult } from '../types/autofill';

console.info('Bitwarden Autofill Content Script loaded');

class BitwardenAutofill {
  private fieldDetector: FieldDetector;
  private overlayManager: OverlayManager;
  private detectedFields: FieldDetectionResult[] = [];
  private currentFocusedField: AutofillField | null = null;
  private isEnabled: boolean = true;

  constructor() {
    this.fieldDetector = new FieldDetector();
    this.overlayManager = new OverlayManager();
    this.init();
  }

  private init(): void {
    this.detectFields();
    this.setupEventListeners();
    this.setupMessageListener();

    // Re-detect fields when DOM changes
    this.observeDOM();
  }

  private detectFields(): void {
    this.detectedFields = this.fieldDetector.scanForFields();
    console.log('Detected autofill fields:', this.detectedFields);
  }

  private setupEventListeners(): void {
    // Field focus handler
    document.addEventListener('focusin', this.handleFieldFocus.bind(this));

    // Field blur handler
    document.addEventListener('focusout', this.handleFieldBlur.bind(this));

    // Window resize and scroll handlers
    window.addEventListener('resize', this.handleWindowChange.bind(this));
    window.addEventListener('scroll', this.handleWindowChange.bind(this), true);
  }

  private setupMessageListener(): void {
    if (typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.onMessage.addListener((message: any, _sender: any, _sendResponse: any) => {
        switch (message.type) {
          case 'SHOW_CREDENTIALS_LIST':
            if (message.data.credentials && this.currentFocusedField) {
              const element = this.getCurrentFieldElement();
              if (element) {
                const position = this.calculateButtonPosition(element);
                this.overlayManager.showCredentialsList(message.data.credentials, position);
              }
            }
            break;
          case 'TOGGLE_AUTOFILL':
            this.isEnabled = message.data.enabled;
            if (!this.isEnabled) {
              this.overlayManager.hideAutofillButton();
            }
            break;
        }
      });
    }
  }

  private observeDOM(): void {
    const observer = new MutationObserver(() => {
      this.detectFields();
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['type', 'name', 'id', 'class', 'placeholder']
    });
  }

  private handleFieldFocus(event: FocusEvent): void {
    if (!this.isEnabled) return;

    const target = event.target as HTMLInputElement;

    // Check if this is a detected autofill field
    const detectedField = this.detectedFields.find(df =>
      this.isMatchingField(df.field, target)
    );

    if (detectedField && detectedField.confidence > 0.6) {
      this.currentFocusedField = detectedField.field;
      const position = this.calculateButtonPosition(target);
      this.overlayManager.showAutofillButton(detectedField.field, position);
    }
  }

  private handleFieldBlur(_event: FocusEvent): void {
    // Delay hiding to allow for button clicks
    setTimeout(() => {
      // Only hide if no overlay is currently visible
      if (!document.querySelector('.bitwarden-credentials-list')) {
        this.overlayManager.hideAutofillButton();
        this.currentFocusedField = null;
      }
    }, 150);
  }

  private handleWindowChange(): void {
    if (this.currentFocusedField) {
      const element = this.getCurrentFieldElement();
      if (element) {
        const position = this.calculateButtonPosition(element);
        this.overlayManager.updatePosition(this.currentFocusedField, position);
      }
    }
  }

  private isMatchingField(field: AutofillField, element: HTMLInputElement): boolean {
    return !!(
      (field.id && field.id === element.id) ||
      (field.name && field.name === element.name) ||
      (field.htmlID && element.matches(`#${field.htmlID}`)) ||
      (field.htmlName && element.matches(`[name="${field.htmlName}"]`))
    );
  }

  private getCurrentFieldElement(): HTMLInputElement | null {
    if (!this.currentFocusedField) return null;

    return document.querySelector(`#${this.currentFocusedField.id}`) as HTMLInputElement ||
           document.querySelector(`[name="${this.currentFocusedField.name}"]`) as HTMLInputElement;
  }

  private calculateButtonPosition(element: HTMLElement): AutofillButtonPosition {
    const rect = element.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    return {
      top: rect.top + scrollTop + (rect.height - 24) / 2, // Center vertically
      left: rect.right + scrollLeft - 28, // Position inside the field, near the right edge
      width: 24,
      height: 24
    };
  }
}

// Initialize the autofill functionality when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new BitwardenAutofill();
  });
} else {
  new BitwardenAutofill();
}
