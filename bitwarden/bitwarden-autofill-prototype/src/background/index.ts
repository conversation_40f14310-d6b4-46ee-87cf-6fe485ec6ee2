import { getCredentialsForDomain, getCardCredentials, getIdentityCredentials } from '../data/mockCredentials';
import { InlineMenuFillType } from '../types/autofill';

console.log('Bitwarden Autofill Background Script loaded');

class BitwardenBackground {
  constructor() {
    this.setupMessageListener();
  }

  private setupMessageListener(): void {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      switch (message.type) {
        case 'GET_CREDENTIALS':
          this.handleGetCredentials(message.data, sender.tab?.id);
          break;
        case 'COUNT':
          console.log('background has received a message from popup, and count is ', message?.count);
          break;
        default:
          console.log('Unknown message type:', message.type);
      }
    });
  }

  private handleGetCredentials(data: { domain: string; fillType: InlineMenuFillType }, tabId?: number): void {
    if (!tabId) return;

    let credentials;

    switch (data.fillType) {
      case InlineMenuFillType.Card:
        credentials = getCardCredentials();
        break;
      case InlineMenuFillType.Identity:
        credentials = getIdentityCredentials();
        break;
      case InlineMenuFillType.Login:
      default:
        credentials = getCredentialsForDomain(data.domain);
        break;
    }

    // Send credentials back to content script
    chrome.tabs.sendMessage(tabId, {
      type: 'SHOW_CREDENTIALS_LIST',
      data: { credentials }
    });
  }
}

// Initialize the background service
new BitwardenBackground();
