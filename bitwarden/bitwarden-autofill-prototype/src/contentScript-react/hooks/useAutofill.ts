import { useCallback, useState } from 'react';
import { 
  Credential, 
  AutofillField, 
  FieldQualifier, 
  CipherType, 
  InlineMenuFillType 
} from '../../types/autofill';

export const useAutofill = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(true); // Mock authenticated state

  const fillField = useCallback((element: HTMLInputElement, value: string) => {
    if (!element || element.disabled || element.readOnly) return;

    // Set the value
    element.value = value;

    // Trigger events to notify frameworks like React
    const events = [
      new Event('input', { bubbles: true }),
      new Event('change', { bubbles: true }),
      new Event('blur', { bubbles: true })
    ];

    events.forEach(event => element.dispatchEvent(event));

    // For React specifically
    const descriptor = Object.getOwnPropertyDescriptor(element, 'value');
    if (descriptor && descriptor.set) {
      descriptor.set.call(element, value);
      element.dispatchEvent(new Event('input', { bubbles: true }));
    }
  }, []);

  const findFieldByQualifier = useCallback((qualifier: FieldQualifier): HTMLInputElement | null => {
    // Try to find by autocomplete attribute first
    let selector = `input[autocomplete="${qualifier}"]`;
    let element = document.querySelector(selector) as HTMLInputElement;
    if (element) return element;

    // Try common name patterns
    const patterns = getFieldPatterns(qualifier);
    for (const pattern of patterns) {
      selector = `input[name*="${pattern}"], input[id*="${pattern}"], input[class*="${pattern}"]`;
      element = document.querySelector(selector) as HTMLInputElement;
      if (element) return element;
    }

    // Try by placeholder text
    for (const pattern of patterns) {
      selector = `input[placeholder*="${pattern}" i]`;
      element = document.querySelector(selector) as HTMLInputElement;
      if (element) return element;
    }

    return null;
  }, []);

  const getFieldPatterns = (qualifier: FieldQualifier): string[] => {
    switch (qualifier) {
      case FieldQualifier.username:
        return ['username', 'user', 'login', 'account'];
      case FieldQualifier.password:
      case FieldQualifier.currentPassword:
        return ['password', 'pass', 'pwd'];
      case FieldQualifier.newPassword:
        return ['new-password', 'newpassword', 'password'];
      case FieldQualifier.email:
        return ['email', 'mail'];
      case FieldQualifier.cardNumber:
        return ['cardnumber', 'card-number', 'ccnumber'];
      case FieldQualifier.cardholderName:
        return ['cardname', 'cardholder', 'cc-name'];
      case FieldQualifier.cardExpirationMonth:
        return ['exp-month', 'expiry-month', 'mm'];
      case FieldQualifier.cardExpirationYear:
        return ['exp-year', 'expiry-year', 'yy', 'yyyy'];
      case FieldQualifier.cardSecurityCode:
        return ['cvv', 'cvc', 'security-code'];
      case FieldQualifier.identityFirstName:
        return ['firstname', 'first-name', 'fname'];
      case FieldQualifier.identityLastName:
        return ['lastname', 'last-name', 'lname'];
      case FieldQualifier.identityFullName:
        return ['name', 'fullname', 'full-name'];
      case FieldQualifier.identityAddress1:
        return ['address', 'address1', 'street'];
      case FieldQualifier.identityCity:
        return ['city', 'town'];
      case FieldQualifier.identityState:
        return ['state', 'province'];
      case FieldQualifier.identityPostalCode:
        return ['zip', 'postal', 'zipcode'];
      case FieldQualifier.identityCountry:
        return ['country'];
      case FieldQualifier.identityPhone:
        return ['phone', 'tel', 'mobile'];
      default:
        return [];
    }
  };

  const fillLoginCredential = useCallback((credential: Credential) => {
    if (!credential.login) return;

    const usernameField = findFieldByQualifier(FieldQualifier.username) || 
                          findFieldByQualifier(FieldQualifier.email);
    const passwordField = findFieldByQualifier(FieldQualifier.password) ||
                          findFieldByQualifier(FieldQualifier.currentPassword);

    if (usernameField && credential.login.username) {
      fillField(usernameField, credential.login.username);
    }

    if (passwordField && credential.login.password) {
      fillField(passwordField, credential.login.password);
    }
  }, [fillField, findFieldByQualifier]);

  const fillCardCredential = useCallback((credential: Credential) => {
    if (!credential.card) return;

    const fields = {
      number: findFieldByQualifier(FieldQualifier.cardNumber),
      name: findFieldByQualifier(FieldQualifier.cardholderName),
      expMonth: findFieldByQualifier(FieldQualifier.cardExpirationMonth),
      expYear: findFieldByQualifier(FieldQualifier.cardExpirationYear),
      cvv: findFieldByQualifier(FieldQualifier.cardSecurityCode)
    };

    if (fields.number && credential.card.number) {
      fillField(fields.number, credential.card.number);
    }
    if (fields.name && credential.card.cardholderName) {
      fillField(fields.name, credential.card.cardholderName);
    }
    if (fields.expMonth && credential.card.expMonth) {
      fillField(fields.expMonth, credential.card.expMonth);
    }
    if (fields.expYear && credential.card.expYear) {
      fillField(fields.expYear, credential.card.expYear);
    }
    if (fields.cvv && credential.card.code) {
      fillField(fields.cvv, credential.card.code);
    }
  }, [fillField, findFieldByQualifier]);

  const fillIdentityCredential = useCallback((credential: Credential) => {
    if (!credential.identity) return;

    const fields = {
      firstName: findFieldByQualifier(FieldQualifier.identityFirstName),
      lastName: findFieldByQualifier(FieldQualifier.identityLastName),
      fullName: findFieldByQualifier(FieldQualifier.identityFullName),
      email: findFieldByQualifier(FieldQualifier.email),
      phone: findFieldByQualifier(FieldQualifier.identityPhone),
      address1: findFieldByQualifier(FieldQualifier.identityAddress1),
      city: findFieldByQualifier(FieldQualifier.identityCity),
      state: findFieldByQualifier(FieldQualifier.identityState),
      postalCode: findFieldByQualifier(FieldQualifier.identityPostalCode),
      country: findFieldByQualifier(FieldQualifier.identityCountry)
    };

    if (fields.firstName && credential.identity.firstName) {
      fillField(fields.firstName, credential.identity.firstName);
    }
    if (fields.lastName && credential.identity.lastName) {
      fillField(fields.lastName, credential.identity.lastName);
    }
    if (fields.fullName && !fields.firstName && !fields.lastName) {
      const fullName = `${credential.identity.firstName || ''} ${credential.identity.lastName || ''}`.trim();
      if (fullName) fillField(fields.fullName, fullName);
    }
    if (fields.email && credential.identity.email) {
      fillField(fields.email, credential.identity.email);
    }
    if (fields.phone && credential.identity.phone) {
      fillField(fields.phone, credential.identity.phone);
    }
    if (fields.address1 && credential.identity.address1) {
      fillField(fields.address1, credential.identity.address1);
    }
    if (fields.city && credential.identity.city) {
      fillField(fields.city, credential.identity.city);
    }
    if (fields.state && credential.identity.state) {
      fillField(fields.state, credential.identity.state);
    }
    if (fields.postalCode && credential.identity.postalCode) {
      fillField(fields.postalCode, credential.identity.postalCode);
    }
    if (fields.country && credential.identity.country) {
      fillField(fields.country, credential.identity.country);
    }
  }, [fillField, findFieldByQualifier]);

  const fillCredential = useCallback((credential: Credential, targetField?: AutofillField) => {
    if (!isAuthenticated) {
      console.warn('User not authenticated');
      return;
    }

    switch (credential.type) {
      case CipherType.Login:
        fillLoginCredential(credential);
        break;
      case CipherType.Card:
        fillCardCredential(credential);
        break;
      case CipherType.Identity:
        fillIdentityCredential(credential);
        break;
      default:
        console.warn('Unsupported credential type:', credential.type);
    }

    // Update last used timestamp
    credential.lastUsed = new Date();
  }, [isAuthenticated, fillLoginCredential, fillCardCredential, fillIdentityCredential]);

  const fillSpecificField = useCallback((field: AutofillField, value: string) => {
    const element = document.querySelector(`#${field.id}`) as HTMLInputElement ||
                   document.querySelector(`[name="${field.name}"]`) as HTMLInputElement;
    
    if (element) {
      fillField(element, value);
    }
  }, [fillField]);

  const generatePassword = useCallback((length: number = 16): string => {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }, []);

  const fillGeneratedPassword = useCallback((targetField?: AutofillField) => {
    const password = generatePassword();
    
    if (targetField) {
      fillSpecificField(targetField, password);
    } else {
      const passwordField = findFieldByQualifier(FieldQualifier.newPassword) ||
                           findFieldByQualifier(FieldQualifier.password);
      if (passwordField) {
        fillField(passwordField, password);
      }
    }
    
    return password;
  }, [generatePassword, fillSpecificField, findFieldByQualifier, fillField]);

  return {
    fillCredential,
    fillSpecificField,
    fillGeneratedPassword,
    generatePassword,
    isAuthenticated,
    setIsAuthenticated
  };
};
